syntax = "proto3";

package moego.service.online_booking.v1;

import "moego/models/organization/v1/staff_availability_def.proto";
import "moego/models/organization/v1/staff_availability_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/online_booking/v1;onlinebookingsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.online_booking.v1";

// GetStaffAvailabilityRequest
message GetStaffAvailabilityRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // staff id list, staff will init when staff_id no exist
  repeated int64 staff_id_list = 3 [(validate.rules).repeated = {unique: true}];
  // availability type
  optional moego.models.organization.v1.AvailabilityType availability_type = 4 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// StaffAvailabilityResponse
message GetStaffAvailabilityResponse {
  // staff available list
  repeated moego.models.organization.v1.StaffAvailability staff_availability_list = 1;
}

// UpdateStaffAvailabilityRequest
message UpdateStaffAvailabilityRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // staff available list
  repeated moego.models.organization.v1.StaffAvailabilityDef staff_availability_list = 3 [(validate.rules).repeated = {min_items: 1}];
}

// get staff availability status
message GetStaffAvailabilityStatusRequest {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // staff id list
  repeated int64 staff_id_list = 2 [(validate.rules).repeated = {unique: true}];
}

// GetStaffAvailabilityStatusResponse
message GetStaffAvailabilityStatusResponse {
  // map staff id to is available
  map<int64, bool> staff_availability = 1;
}

// UpdateStaffAvailabilityResponse
message UpdateStaffAvailabilityResponse {}

// ListSlotFreeServicesRequest
message ListSlotFreeServicesRequest {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // staff ids
  repeated int64 staff_ids = 2 [(validate.rules).repeated = {unique: true}];
}

// ListSlotFreeServicesResponse
message ListSlotFreeServicesResponse {
  // slot free staff service defs
  repeated models.organization.v1.SlotFreeStaffServiceDef defs = 1 [(validate.rules).repeated = {max_items: 100}];
}

// UpdateSlotFreeServicesRequest
message UpdateSlotFreeServicesRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // slot free staff service defs
  repeated models.organization.v1.SlotFreeStaffServiceDef defs = 3 [(validate.rules).repeated = {max_items: 100}];
}

// UpdateSlotFreeServicesResponse
message UpdateSlotFreeServicesResponse {}

// staff available service
service OBStaffAvailabilityService {
  // get staff available
  rpc GetStaffAvailability(GetStaffAvailabilityRequest) returns (GetStaffAvailabilityResponse);
  // update staff available
  rpc UpdateStaffAvailability(UpdateStaffAvailabilityRequest) returns (UpdateStaffAvailabilityResponse);
  // get staff availability status
  rpc GetStaffAvailabilityStatus(GetStaffAvailabilityStatusRequest) returns (GetStaffAvailabilityStatusResponse);

  // list slot free services
  rpc ListSlotFreeServices(ListSlotFreeServicesRequest) returns (ListSlotFreeServicesResponse);
  // update slot free services
  rpc UpdateSlotFreeServices(UpdateSlotFreeServicesRequest) returns (UpdateSlotFreeServicesResponse);
}
