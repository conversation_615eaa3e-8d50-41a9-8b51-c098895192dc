package com.moego.svc.organization.service;

import com.moego.svc.organization.entity.StaffSlotFreeService;
import com.moego.svc.organization.entity.StaffSlotFreeServiceExample;
import com.moego.svc.organization.mapper.base.BaseStaffSlotFreeServiceMapper;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class StaffSlotFreeServiceService {

    private final BaseStaffSlotFreeServiceMapper staffSlotFreeServiceMapper;

    /**
     * 根据员工ID列表查询相关的服务记录
     *
     */
    public List<StaffSlotFreeService> getByStaffIds(Long businessId, List<Long> staffIds) {
        if (CollectionUtils.isEmpty(staffIds)) {
            return List.of();
        }

        StaffSlotFreeServiceExample example = new StaffSlotFreeServiceExample();
        example.createCriteria().andBusinessIdEqualTo(businessId).andStaffIdIn(staffIds);

        return staffSlotFreeServiceMapper.selectByExample(example);
    }

    /**
     * 删除指定员工的现有记录并插入新记录
     *
     */
    @Transactional
    public int deleteAndInsert(Long companyId, Long businessId, Long staffId, List<Long> serviceIds) {
        int insertedCount = 0;

        // 删除现有记录
        StaffSlotFreeServiceExample deleteExample = new StaffSlotFreeServiceExample();
        deleteExample.createCriteria().andBusinessIdEqualTo(businessId).andStaffIdEqualTo(staffId);
        staffSlotFreeServiceMapper.deleteByExample(deleteExample);

        // 批量插入新记录
        if (!CollectionUtils.isEmpty(serviceIds)) {
            for (Long serviceId : serviceIds) {
                var staffSlotFreeServiceRecord = new StaffSlotFreeService();
                staffSlotFreeServiceRecord.setCompanyId(companyId);
                staffSlotFreeServiceRecord.setBusinessId(businessId);
                staffSlotFreeServiceRecord.setStaffId(staffId);
                staffSlotFreeServiceRecord.setServiceId(serviceId);
                staffSlotFreeServiceMapper.insertSelective(staffSlotFreeServiceRecord);
                insertedCount++;
            }
        }

        return insertedCount;
    }
}
